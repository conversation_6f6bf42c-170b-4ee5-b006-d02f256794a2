websites = {
    "youtube": "www.youtube.com",
    "facebook": "www.facebook.com",
    "github": "www.github.com",
    "youtube studio": "studio.youtube.com",
    "twitter": "www.twitter.com",
    "instagram": "www.instagram.com",
    "linkedin": "www.linkedin.com",
    "wikipedia": "www.wikipedia.org",
    "reddit": "www.reddit.com",
    "pinterest": "www.pinterest.com",
    "quora": "www.quora.com",
    "tumblr": "www.tumblr.com",
    "flickr": "www.flickr.com",
    "snapchat": "www.snapchat.com",
    "tiktok": "www.tiktok.com",
    "vimeo": "www.vimeo.com",
    "dropbox": "www.dropbox.com",
    "onedrive": "www.onedrive.com",
    "google drive": "drive.google.com",
    "icloud": "www.icloud.com",
    "amazon": "www.amazon.com",
    "ebay": "www.ebay.com",
    "alibaba": "www.alibaba.com",
    "netflix": "www.netflix.com",
    "hulu": "www.hulu.com",
    "disney plus": "www.disneyplus.com",
    "hbo max": "www.hbomax.com",
    "spotify": "www.spotify.com",
    "soundcloud": "www.soundcloud.com",
    "apple music": "www.apple.com/apple-music",
    "pandora": "www.pandora.com",
    "deezer": "www.deezer.com",
    "bandcamp": "www.bandcamp.com",
    "bbc": "www.bbc.com",
    "cnn": "www.cnn.com",
    "nytimes": "www.nytimes.com",
    "the guardian": "www.theguardian.com",
    "forbes": "www.forbes.com",
    "bloomberg": "www.bloomberg.com",
    "reuters": "www.reuters.com",
    "espn": "www.espn.com",
    "fox news": "www.foxnews.com",
    "nbc news": "www.nbcnews.com",
    "cbs news": "www.cbsnews.com",
    "abc news": "www.abcnews.go.com",
    "msnbc": "www.msnbc.com",
    "npr": "www.npr.org",
    "wsj": "www.wsj.com",
    "yahoo news": "news.yahoo.com",
    "buzzfeed": "www.buzzfeed.com",
    "huffpost": "www.huffpost.com",
    "canva": "www.canva.com",
    "chatgpt": "chat.openai.com",
    "slack": "www.slack.com",
    "trello": "www.trello.com",
    "asana": "www.asana.com",
    "zoom": "www.zoom.us",
    "skype": "www.skype.com",
    "microsoft teams": "www.microsoft.com/microsoft-teams",
    "google meet": "meet.google.com",
    "webex": "www.webex.com",
    "jira": "www.atlassian.com/software/jira",
    "notion": "www.notion.so",
    "airtable": "www.airtable.com",
    "monday": "www.monday.com",
    "clickup": "www.clickup.com",
    "dropbox paper": "www.dropbox.com/paper",
    "confluence": "www.atlassian.com/software/confluence",
    "figma": "www.figma.com",
    "adobe xd": "www.adobe.com/products/xd.html",
    "invision": "www.invisionapp.com",
    "microsoft word": "www.microsoft.com/microsoft-365/word",
    "google docs": "docs.google.com",
    "medium": "www.medium.com",
    "wordpress": "www.wordpress.com",
    "wix": "www.wix.com",
    "squarespace": "www.squarespace.com",
    "shopify": "www.shopify.com",
    "bigcommerce": "www.bigcommerce.com",
    "weebly": "www.weebly.com",
    "godaddy": "www.godaddy.com",
    "namecheap": "www.namecheap.com",
    "bluehost": "www.bluehost.com",
    "siteground": "www.siteground.com",
    "hostgator": "www.hostgator.com",
    "dreamhost": "www.dreamhost.com",
    "a2 hosting": "www.a2hosting.com",
    "inmotion hosting": "www.inmotionhosting.com",
    "digitalocean": "www.digitalocean.com",
    "linode": "www.linode.com",
    "aws": "aws.amazon.com",
    "azure": "azure.microsoft.com",
    "google cloud": "cloud.google.com",
    "heroku": "www.heroku.com",
    "gitlab": "www.gitlab.com",
    "bitbucket": "bitbucket.org",
    "codepen": "codepen.io",
    "jsfiddle": "jsfiddle.net",
    "repl.it": "repl.it",
    "stack overflow": "stackoverflow.com",
    "stackoverflow careers": "stackoverflow.com/jobs",
    "glassdoor": "www.glassdoor.com",
    "indeed": "www.indeed.com",
    "linkedin jobs": "www.linkedin.com/jobs",
    "monster": "www.monster.com",
    "simplyhired": "www.simplyhired.com",
    "angel.co": "angel.co",
    "github jobs": "jobs.github.com",
    "ziprecruiter": "www.ziprecruiter.com",
    "careerbuilder": "www.careerbuilder.com",
    "snagajob": "www.snagajob.com",
    "dice": "www.dice.com",
    "jobs": "www.jobs.com",
    "bamboohr": "www.bamboohr.com",
    "workday": "www.workday.com",
    "adp": "www.adp.com",
    "sap successfactors": "www.sap.com/products/hcm.html",
    "oracle hcm": "www.oracle.com/applications/human-capital-management",
    "zenefits": "www.zenefits.com",
    "paycor": "www.paycor.com",
    "paycom": "www.paycom.com",
    "gusto": "www.gusto.com",
    "square": "squareup.com",
    "stripe": "www.stripe.com",
    "paypal": "www.paypal.com",
    "venmo": "www.venmo.com",
    "cash app": "cash.app",
    "robinhood": "www.robinhood.com",
    "etrade": "www.etrade.com",
    "fidelity": "www.fidelity.com",
    "charles schwab": "www.schwab.com",
    "vanguard": "investor.vanguard.com",
    "td ameritrade": "www.tdameritrade.com",
    "coinbase": "www.coinbase.com",
    "binance": "www.binance.com",
    "kraken": "www.kraken.com",
    "blockchain": "www.blockchain.com",
    "gemini": "www.gemini.com",
    "bitfinex": "www.bitfinex.com",
    "bitstamp": "www.bitstamp.net",
    "bittrex": "www.bittrex.com",
    "okex": "www.okex.com",
    "poloniex": "www.poloniex.com",
    "coindesk": "www.coindesk.com",
    "cointelegraph": "www.cointelegraph.com",
    "decrypt": "www.decrypt.co",
    "cryptoslate": "www.cryptoslate.com",
    "cryptonews": "www.cryptonews.com",
    "coinmarketcap": "www.coinmarketcap.com",
    "coingecko": "www.coingecko.com",
    "messari": "www.messari.io",
    "icodrops": "www.icodrops.com",
    "tokenmarket": "www.tokenmarket.net",
    "coinpaprika": "www.coinpaprika.com",
    "cryptocompare": "www.cryptocompare.com",
    "coincheckup": "www.coincheckup.com",
    "cryptobriefing": "www.cryptobriefing.com",
    "blockonomi": "www.blockonomi.com",
    "coininsider": "www.coininsider.com",
    "newsbtc": "www.newsbtc.com",
    "bitcoin.com": "www.bitcoin.com",
    "ethereum.org": "www.ethereum.org",
    "litecoin.com": "www.litecoin.com",
    "ripple.com": "www.ripple.com",
    "cardano.org": "www.cardano.org",
    "stellarlumens.com": "www.stellarlumens.com",
    "tezos.com": "www.tezos.com",
    "eos.io": "www.eos.io",
    "neo.org": "www.neo.org",
    "iota.org": "www.iota.org",
    "monero.org": "www.monero.org",
    "zcash.org": "www.zcash.org",
    "dash.org": "www.dash.org",
    "dogecoin.com": "www.dogecoin.com",
    "gpt":"www.chatgpt.com/"
}
