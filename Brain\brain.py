# from webscout import PhindSearch as brain


# ai = brain(
#     is_conversation=True,
#     max_tokens=800,
#     timeout=30,
#     intro='J.A.R.V.I.S',
#     filepath=r"C:\Users\<USER>\Desktop\J.A.R.V.I.S\chat_hystory.txt",
#     update_file=True,
#     proxies={},
#     history_offset=10250,
#     act=None,
# )

# def Main_Brain(text):
#     r = ai.chat(text)
#     return r 

from webscout import PhindSearch
from os import getcwd
import os

def Main_Brain(text):
    # Use the current working directory for the chat history file
    chat_history_path = os.path.join(getcwd(), "chat_hystory.txt")

    try:
        ai = PhindSearch(quiet=True, filepath=chat_history_path, is_conversation=None)
        res = ai.chat(text) # internal stream is not available for this Provider
        return res
    except Exception as e:
        print(f"Error in Main_Brain: {e}")
        return "I'm having trouble processing that request right now."

