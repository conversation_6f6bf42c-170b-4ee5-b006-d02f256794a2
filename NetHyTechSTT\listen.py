from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from os import getcwd
import os

# Global variables
Recog_File = f"{getcwd()}\\input.txt"
website = "https://allorizenproject1.netlify.app/"

def setup_chrome_driver():
    """Setup Chrome driver with proper options and error handling"""
    chrome_options = Options()
    chrome_options.add_argument("--use-fake-ui-for-media-stream")
    chrome_options.add_argument("--headless=new")  # Use new headless mode
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")
    chrome_options.add_argument("--window-size=1920,1080")

    try:
        # Use the local chromedriver.exe
        chrome_driver_path = f"{getcwd()}\\chromedriver.exe"
        if os.path.exists(chrome_driver_path):
            service = Service(executable_path=chrome_driver_path)
            print("Using local chromedriver.exe")
            driver = webdriver.Chrome(service=service, options=chrome_options)
            return driver
        else:
            print(f"ChromeDriver not found at: {chrome_driver_path}")
            print("Please make sure chromedriver.exe is in the project directory.")
            return None

    except Exception as e:
        print(f"Failed to setup Chrome driver: {e}")
        print("Please make sure Chrome browser is installed and accessible.")
        return None
def listen_fallback():
    """Fallback text input method when Chrome WebDriver is not available"""
    print("Chrome WebDriver not available. Using text input mode.")
    print("Type your commands and press Enter. Type 'exit' to quit.")

    while True:
        try:
            user_input = input("You: ").strip()
            if user_input.lower() == 'exit':
                break
            if user_input:
                with open(Recog_File, "w") as file:
                    file.write(user_input.lower())
                print(f"Command saved: {user_input}")
        except KeyboardInterrupt:
            print("\nExiting text input mode...")
            break
        except Exception as e:
            print(f"Error in text input: {e}")

def listen():
    print("Support in Youtube @NetHyTech")
    print("Initializing Chrome driver for speech recognition...")

    # Setup Chrome driver
    driver = setup_chrome_driver()
    if driver is None:
        print("Chrome WebDriver failed to initialize.")
        print("Falling back to text input mode...")
        listen_fallback()
        return

    try:
        # Open the website
        print("Opening speech recognition website...")
        driver.get(website)

        start_button = WebDriverWait(driver, 20).until(EC.element_to_be_clickable((By.ID, 'startButton')))
        start_button.click()
        print("Listening...")
        output_text = ""
        is_second_click = False
        while True:
            output_element = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, 'output')))
            current_text = output_element.text.strip()
            if "Start Listening" in start_button.text and is_second_click:
                if output_text:
                    is_second_click = False
            elif "Listening..." in start_button.text:
                is_second_click = True
            if current_text != output_text:
                output_text = current_text
                with open(Recog_File, "w") as file:
                    file.write(output_text.lower())
                    print("User:", output_text)
    except KeyboardInterrupt:
        print("Process interrupted by user.")
    except Exception as e:
        print("An error occurred:", e)
        print("Speech recognition may not be working properly.")
        print("Falling back to text input mode...")
        listen_fallback()
    finally:
        if driver:
            try:
                driver.quit()
            except:
                pass